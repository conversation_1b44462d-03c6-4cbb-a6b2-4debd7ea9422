/**
 * Main类 - 程序入口点
 * 对应设计文档中的主程序模块，负责程序启动和模块协调
 * 
 * 对应需求：
 * - NFR-001: 使用JDK 1.8开发
 * - NFR-002: 使用Swing作为GUI框架（已从SWT改为Swing）
 * - 系统架构中的程序入口点
 * - MVC各模块的初始化和协调
 */
public class Main {
    /**
     * 程序主入口点
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            System.out.println("=== 15-Puzzle Game Starting ===");
            System.out.println("Java Version: " + System.getProperty("java.version"));
            System.out.println("OS: " + System.getProperty("os.name"));
            System.out.println("GUI Framework: Swing (Built-in)");
            System.out.println();
            
            // 设置Swing界面样式
            try {
                // 使用系统默认样式
                javax.swing.UIManager.setLookAndFeel(
                    javax.swing.UIManager.getSystemLookAndFeelClassName()
                );
            } catch (Exception e) {
                System.out.println("警告: 无法设置系统外观，使用默认外观");
            }
            
            // 创建游戏控制器 (MVC中的Controller)
            PuzzleGame game = new PuzzleGame();
            
            // 创建用户界面 (MVC中的View)
            PuzzleGameUI ui = new PuzzleGameUI(game);
            
            System.out.println("UI initialized, game started!");
            System.out.println("=== Game Instructions ===");
            System.out.println("- Click on numbered tiles adjacent to the empty space to move them");
            System.out.println("- Goal: arrange numbers 1-15 in order with empty space at bottom-right");
            System.out.println("- Movable tiles are highlighted in light green");
            System.out.println("- Click 'Reset' button to shuffle the puzzle");
            System.out.println("- A victory message will appear when you win");
            System.out.println("========================");
            System.out.println();
            
            // 启动用户界面
            ui.start();
            
        } catch (Exception e) {
            System.err.println("Program startup failed:");
            e.printStackTrace();
            
            System.err.println();
            System.err.println("=== Troubleshooting Tips ===");
            System.err.println("1. Ensure JDK 1.8 or higher is installed");
            System.err.println("2. Swing is built into Java - no extra installation needed");
            System.err.println("3. Make sure you have a graphical environment (not command-line only)");
            System.err.println("4. Try running with these commands:");
            System.err.println("   javac *.java");
            System.err.println("   java Main");
            System.err.println("==========================");
            
            System.exit(1);
        }
    }
}