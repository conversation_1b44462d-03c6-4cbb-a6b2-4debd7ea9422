# 数字拼图游戏需求文档 (PRD)

## 1. 项目概述

### 1.1 项目名称
数字拼图游戏（15-Puzzle Game）

### 1.2 项目背景
开发一款经典的数字滑块拼图游戏，用户通过移动数字方块来完成从乱序到有序的拼图挑战。

### 1.3 项目目标
实现一个功能完整、界面友好的数字拼图游戏，具备基本的游戏逻辑和用户交互功能。

## 2. 功能需求

### 2.1 游戏界面需求
- **FR-001**: 游戏界面必须显示一个4×4的数字网格
- **FR-002**: 网格中包含数字1-15和一个空白格
- **FR-003**: 每个数字方块必须清晰显示对应数字
- **FR-004**: 空白格必须在视觉上与数字方块区分开

### 2.2 游戏交互需求
- **FR-005**: 用户点击与空白格相邻的数字方块时，该方块移动到空白格位置
- **FR-006**: 只有与空白格水平或垂直相邻的方块才能移动
- **FR-007**: 对角线相邻的方块不能移动
- **FR-008**: 点击不可移动的方块时，游戏应无反应

### 2.3 游戏逻辑需求
- **FR-009**: 游戏开始时，数字方块必须随机打乱排列
- **FR-010**: 游戏必须判断当前排列是否为胜利状态（数字1-15按顺序排列，空格在右下角）
- **FR-011**: 达到胜利状态时，游戏必须显示胜利提示信息
- **FR-012**: 提供"重新开始"功能，重新打乱数字方块

### 2.4 界面功能需求
- **FR-013**: 必须提供"重置游戏"按钮
- **FR-014**: 必须显示当前游戏状态（进行中/胜利）
- **FR-015**: 界面布局必须简洁明了，易于操作

## 3. 非功能需求

### 3.1 技术约束
- **NFR-001**: 必须使用Java JDK 1.8开发
- **NFR-002**: 必须使用Swing作为GUI框架
- **NFR-003**: 代码必须遵循面向对象设计原则
- **NFR-004**: 必须实现MVC设计模式

### 3.2 性能要求
- **NFR-005**: 方块移动响应时间不超过100毫秒
- **NFR-006**: 胜利判断响应时间不超过50毫秒
- **NFR-007**: 游戏重置时间不超过200毫秒

### 3.3 用户体验要求
- **NFR-008**: 界面必须直观易懂，无需说明即可使用
- **NFR-009**: 所有按钮必须有明确的视觉反馈
- **NFR-010**: 游戏必须支持连续游戏，无需重启程序

## 4. 系统架构要求

### 4.1 模块划分
- **数据模型模块**: 负责游戏状态数据管理
- **游戏逻辑模块**: 负责游戏规则和算法实现
- **用户界面模块**: 负责界面显示和用户交互
- **主程序模块**: 负责程序启动和模块协调

### 4.2 核心类设计要求
- 必须包含GameBoard类：管理4×4数字网格数据
- 必须包含PuzzleGame类：控制游戏流程和状态
- 必须包含PuzzleGameUI类：实现Swing用户界面
- 必须包含Main类：程序入口点

## 5. 验收标准

### 5.1 功能验收
- ✓ 4×4网格正确显示数字1-15和空白格
- ✓ 只能移动与空格相邻的数字方块
- ✓ 正确判断并提示胜利状态
- ✓ 重置功能正常工作
- ✓ 界面响应流畅，无明显延迟

### 5.2 技术验收
- ✓ 使用JDK 1.8编译运行正常
- ✓ 使用Swing实现界面
- ✓ 代码结构清晰，遵循OOP原则
- ✓ 实现MVC模式分离

---

**文档版本**: v1.0  
**创建日期**: 2025-08-11  
**最后更新**: 2025-08-11