# AI辅助开发演示操作脚本

## 演示总时长：20分钟

---

## 第一阶段：需求文档展示（2分钟）

### 操作1：打开需求文档
**你要说的话**：
> "大家好，今天我要演示AI如何帮助我们从需求文档到设计文档，再到最终代码的完整开发流程。这里是我们的项目需求文档。"

**操作步骤**：
1. 打开文件：`数字拼图游戏需求文档.md`

### 操作2：快速浏览重点需求
**你要说的话**：
> "请注意这几个关键需求点，待会我们要验证AI生成的设计和代码是否完全符合这些要求：
> - FR-001: 必须是4×4数字网格
> - FR-005: 点击相邻方块可移动
> - FR-010: 自动判断胜利状态
> - NFR-001: 使用JDK 1.8
> - NFR-002: 使用Swing界面框架
> 现在让AI根据这份需求文档生成设计文档。"

**操作步骤**：
1. 滚动显示需求文档的重点部分（第2节功能需求）

---

## 第二阶段：AI生成设计文档（6分钟）

### 操作3：启动AI设计文档生成
**你要说的话**：
> "现在我要求AI严格按照需求文档生成详细的设计文档，包括系统架构图、类图、状态图和时序图。"

**操作步骤**：
1. 在AI界面输入以下提示词：

```
请根据"数字拼图游戏需求文档.md"生成完整的设计文档，要求：

1. 系统架构设计：
   - 按照需求NFR-004实现MVC模式
   - 明确各模块职责和接口

2. 详细类图设计：
   - 根据需求4.2节的核心类要求设计
   - GameBoard类：管理4×4网格数据（FR-001）
   - PuzzleGame类：游戏逻辑控制
   - PuzzleGameUI类：SWT界面实现（NFR-002）
   - Main类：程序入口

3. 状态图设计：
   - 体现FR-010胜利判断逻辑
   - 显示游戏状态转换

4. 时序图设计：
   - 详细展示FR-005点击移动的完整流程
   - 从用户点击到界面更新的全过程

5. 界面设计原型：
   - 4×4按钮网格布局（FR-001, FR-003）
   - 重置按钮位置（FR-013）

请确保设计的每个元素都能在需求文档中找到对应的需求编号。
```

### 操作4：分析设计文档的需求符合性
**你要说的话**：
> "大家看，AI生成的设计文档完全对应了我们的需求：
> - 类图中的GameBoard类正是需求4.2要求的核心类
> - 4×4网格设计对应FR-001需求
> - Swing界面设计对应NFR-002需求
> - 状态图体现了FR-010的胜利判断逻辑
> - 时序图详细展示了FR-005的点击移动流程
> 每个设计元素都能在需求文档中找到依据。"

**操作步骤**：
1. 等AI生成设计文档
2. 逐一对比设计文档和需求文档的对应关系

---

## 第三阶段：AI生成代码实现（10分钟）

### 操作5：生成数据模型类
**你要说的话**：
> "现在让AI根据设计文档生成代码。首先是GameBoard类，注意看它如何实现设计文档中定义的数据结构和方法。"

**操作步骤**：
1. 输入提示词：

```
请根据设计文档中的类图，生成GameBoard.java类，要求：
1. 严格按照类图中定义的属性和方法
2. 实现4×4数字网格的数据存储（对应FR-001）
3. 实现方块移动逻辑（对应FR-005, FR-006, FR-007）
4. 实现胜利状态判断（对应FR-010）
5. 实现随机打乱功能（对应FR-009）
请使用JDK 1.8兼容的语法。
```

### 操作6：生成游戏逻辑类
**你要说的话**：
> "接下来是PuzzleGame类，这是设计文档中定义的游戏控制器，负责协调各个模块。"

**操作步骤**：
1. 输入提示词：

```
请根据设计文档生成PuzzleGame.java类，要求：
1. 按照MVC模式作为Controller层（对应NFR-004）
2. 管理游戏状态转换（参考状态图设计）
3. 协调GameBoard和UI层的交互
4. 实现重置游戏功能（对应FR-012）
使用JDK 1.8语法。
```

### 操作7：生成Swing界面类
**你要说的话**：
> "现在生成Swing界面类，这要严格按照设计文档的界面原型和时序图实现。"

**操作步骤**：
1. 输入提示词：

```
请根据设计文档生成PuzzleGameUI.java类，要求：
1. 使用Swing框架（对应NFR-002）
2. 创建4×4按钮网格（对应FR-001, FR-003）
3. 实现点击事件处理（按照时序图流程）
4. 显示游戏状态和胜利提示（对应FR-011, FR-014）
5. 添加重置按钮（对应FR-013）
6. 确保响应时间符合性能要求（NFR-005, NFR-006）
使用JDK 1.8内置的Swing库。
```

### 操作8：生成主程序入口
**你要说的话**：
> "最后生成Main类，这是整个系统的入口点。"

**操作步骤**：
1. 输入提示词：

```
请根据设计文档生成Main.java类，要求：
1. 作为程序入口点
2. 初始化各个模块
3. 启动Swing界面
4. 确保程序能在JDK 1.8环境运行
```

### 操作9：验证代码与设计的一致性
**你要说的话**：
> "大家注意看，生成的每个类都严格按照设计文档实现：
> - GameBoard类的方法和属性与类图完全一致
> - 界面布局与原型图完全匹配
> - 交互流程与时序图完全对应
> 这就是AI确保设计文档和代码实现一致性的体现。"

---

## 第四阶段：编译运行验证（2分钟）

### 操作10：编译项目
**你要说的话**：
> "现在编译运行程序，验证最终软件是否完全满足需求文档的所有要求。"

**操作步骤**：
1. 输入编译命令：

```
javac *.java
java Main
```

### 操作11：功能验证
**你要说的话**：
> "程序运行成功！现在逐一验证需求文档的要求：
> ✓ FR-001: 4×4数字网格 - 完全符合
> ✓ FR-005: 点击移动功能 - 完全符合  
> ✓ FR-010: 胜利判断 - 完全符合
> ✓ FR-013: 重置按钮 - 完全符合
> ✓ NFR-001: JDK 1.8 - 完全符合
> ✓ NFR-002: Swing界面 - 完全符合"

**操作步骤**：
1. 点击几个数字方块演示移动功能
2. 点击重置按钮演示重置功能
3. 如果有时间，尝试完成拼图演示胜利状态

---

## 总结发言（30秒）

**你要说的话**：
> "通过这个演示，我们看到AI能够：
> 1. 准确理解需求文档的每个细节
> 2. 生成完全符合需求的设计文档
> 3. 基于设计文档生成规范的代码实现
> 整个过程保持了完美的一致性和可追溯性，这就是AI辅助开发的强大之处。"

---

## 时间分配检查表
- [x] 需求展示: 2分钟
- [x] 设计生成: 6分钟  
- [x] 代码生成: 10分钟
- [x] 运行验证: 2分钟
- **总计: 20分钟**

## 备注
1. 如果AI生成内容太长，可以说"由于时间关系，我们快速浏览关键部分"
2. 如果编译出错，提前准备好SWT库路径
3. 保持节奏，每个步骤不要超时