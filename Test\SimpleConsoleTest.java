/**
 * 简单控制台测试类
 * 用于验证GameBoard和PuzzleGame的核心逻辑
 * 不依赖SWT，确保核心代码逻辑正确
 */
public class SimpleConsoleTest {
    public static void main(String[] args) {
        System.out.println("=== 数字拼图游戏核心逻辑测试 ===");
        
        // 测试GameBoard类
        testGameBoard();
        
        // 测试PuzzleGame类
        testPuzzleGame();
        
        System.out.println("=== 所有核心逻辑测试完成 ===");
    }
    
    /**
     * 测试GameBoard类的功能
     */
    private static void testGameBoard() {
        System.out.println("\n1. 测试GameBoard类：");
        
        GameBoard board = new GameBoard();
        
        // 显示初始状态
        System.out.println("初始棋盘状态：");
        printBoard(board.getBoard());
        
        // 测试胜利判断
        System.out.println("当前是否胜利: " + board.isWinning());
        
        // 测试移动功能
        int[][] currentBoard = board.getBoard();
        int[] emptyPos = board.getEmptyPosition();
        System.out.println("空格位置: [" + emptyPos[0] + ", " + emptyPos[1] + "]");
        
        // 寻找一个可以移动的方块
        boolean foundValidMove = false;
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (board.isValidMove(i, j)) {
                    System.out.println("尝试移动位置 [" + i + ", " + j + "] 的数字 " + currentBoard[i][j]);
                    boolean moved = board.moveNumber(i, j);
                    System.out.println("移动结果: " + (moved ? "成功" : "失败"));
                    if (moved) {
                        System.out.println("移动后棋盘状态：");
                        printBoard(board.getBoard());
                        foundValidMove = true;
                        break;
                    }
                }
            }
            if (foundValidMove) break;
        }
        
        System.out.println("✓ GameBoard类测试完成");
    }
    
    /**
     * 测试PuzzleGame类的功能
     */
    private static void testPuzzleGame() {
        System.out.println("\n2. 测试PuzzleGame类：");
        
        PuzzleGame game = new PuzzleGame();
        
        System.out.println("游戏状态: " + game.getGameStateText());
        
        // 测试移动处理
        int[][] board = game.getCurrentBoard();
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (game.isValidMove(i, j)) {
                    System.out.println("通过PuzzleGame移动 [" + i + ", " + j + "]");
                    game.handleMove(i, j);
                    System.out.println("移动后游戏状态: " + game.getGameStateText());
                    break;
                }
            }
        }
        
        // 测试重置功能
        System.out.println("测试重置功能...");
        game.resetGame();
        System.out.println("重置后游戏状态: " + game.getGameStateText());
        
        System.out.println("✓ PuzzleGame类测试完成");
    }
    
    /**
     * 打印棋盘状态
     */
    private static void printBoard(int[][] board) {
        System.out.println("┌─────┬─────┬─────┬─────┐");
        for (int i = 0; i < 4; i++) {
            System.out.print("│");
            for (int j = 0; j < 4; j++) {
                if (board[i][j] == 0) {
                    System.out.print("     │"); // 空格
                } else {
                    System.out.printf("%4d │", board[i][j]);
                }
            }
            System.out.println();
            if (i < 3) {
                System.out.println("├─────┼─────┼─────┼─────┤");
            }
        }
        System.out.println("└─────┴─────┴─────┴─────┘");
    }
}