/**
 * PuzzleGame类 - 游戏控制器
 * 对应设计文档中的Controller层，按照MVC模式协调Model和View
 * 
 * 对应需求：
 * - NFR-004: MVC设计模式实现
 * - FR-012: 重置游戏功能协调
 * - 游戏状态管理和流程控制
 */
public class PuzzleGame {
    private GameBoard gameBoard;    // Model层引用
    private PuzzleGameUI ui;       // View层引用
    private GameState gameState;   // 当前游戏状态
    
    /**
     * 游戏状态枚举
     */
    public enum GameState {
        PLAYING,    // 进行中
        WON         // 胜利
    }
    
    /**
     * 构造函数
     */
    public PuzzleGame() {
        gameBoard = new GameBoard();
        gameState = GameState.PLAYING;
    }
    
    /**
     * 设置UI引用（在UI创建后调用）
     * @param ui 用户界面对象
     */
    public void setUI(PuzzleGameUI ui) {
        this.ui = ui;
    }
    
    /**
     * 开始新游戏
     */
    public void startNewGame() {
        gameBoard.reset();
        gameState = GameState.PLAYING;
        if (ui != null) {
            ui.updateDisplay();
        }
    }
    
    /**
     * 处理移动请求 (对应时序图中的移动流程)
     * @param row 要移动的方块行坐标
     * @param col 要移动的方块列坐标
     */
    public void handleMove(int row, int col) {
        // 只有在游戏进行中才处理移动
        if (gameState != GameState.PLAYING) {
            return;
        }
        
        // 尝试移动方块
        boolean moved = gameBoard.moveNumber(row, col);
        
        if (moved) {
            // 检查游戏状态
            checkGameState();
            
            // 更新界面显示
            if (ui != null) {
                ui.updateDisplay();
                
                // 如果获胜，显示胜利消息
                if (gameState == GameState.WON) {
                    ui.showWinMessage();
                }
            }
        }
    }
    
    /**
     * 检查并更新游戏状态
     */
    private void checkGameState() {
        if (gameBoard.isWinning()) {
            gameState = GameState.WON;
        }
    }
    
    /**
     * 重置游戏 (对应FR-012: 重置功能)
     */
    public void resetGame() {
        startNewGame();
    }
    
    /**
     * 获取当前游戏棋盘
     * @return 当前棋盘状态
     */
    public int[][] getCurrentBoard() {
        return gameBoard.getBoard();
    }
    
    /**
     * 获取当前游戏状态
     * @return 游戏状态
     */
    public GameState getGameState() {
        return gameState;
    }
    
    /**
     * 判断指定位置的移动是否有效
     * @param row 行坐标
     * @param col 列坐标
     * @return 是否可以移动
     */
    public boolean isValidMove(int row, int col) {
        return gameBoard.isValidMove(row, col);
    }
    
    /**
     * 获取游戏状态的文本描述 (对应FR-014: 状态显示)
     * @return 状态描述字符串
     */
    public String getGameStateText() {
        switch (gameState) {
            case PLAYING:
                return "进行中";
            case WON:
                return "胜利！";
            default:
                return "未知状态";
        }
    }
}