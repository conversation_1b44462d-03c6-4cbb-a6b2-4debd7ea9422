# 数字拼图游戏设计文档

## 1. 系统架构设计

### 1.1 MVC架构模式 (对应NFR-004)
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│     View        │    │   Controller     │    │     Model       │
│  PuzzleGameUI   │◄──►│   PuzzleGame     │◄──►│   GameBoard     │
│  (SWT界面)      │    │   (游戏控制)     │    │   (数据模型)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────┐
                       │     Main     │
                       │  (程序入口)  │
                       └──────────────┘
```

### 1.2 模块职责划分
- **Model (GameBoard)**: 管理4×4数字网格数据，实现游戏逻辑算法
- **View (PuzzleGameUI)**: SWT界面显示，用户交互处理
- **Controller (PuzzleGame)**: 协调Model和View，控制游戏状态
- **Main**: 程序启动入口，初始化各模块

## 2. 详细类图设计

### 2.1 GameBoard类 (对应FR-001)
```
┌─────────────────────────────────────┐
│              GameBoard              │
├─────────────────────────────────────┤
│ - board: int[][]                    │  ← 4×4数字网格数据
│ - emptyRow: int                     │  ← 空格行坐标
│ - emptyCol: int                     │  ← 空格列坐标
├─────────────────────────────────────┤
│ + GameBoard()                       │  ← 构造函数
│ + initializeBoard(): void           │  ← FR-009: 随机打乱
│ + moveNumber(row, col): boolean     │  ← FR-005: 移动方块
│ + isValidMove(row, col): boolean    │  ← FR-006,FR-007: 移动判断
│ + isWinning(): boolean              │  ← FR-010: 胜利判断
│ + getBoard(): int[][]               │  ← 获取当前状态
│ + reset(): void                     │  ← FR-012: 重置游戏
└─────────────────────────────────────┘
```

### 2.2 PuzzleGame类 (MVC Controller)
```
┌─────────────────────────────────────┐
│            PuzzleGame               │
├─────────────────────────────────────┤
│ - gameBoard: GameBoard              │  ← Model引用
│ - ui: PuzzleGameUI                  │  ← View引用
│ - gameState: GameState              │  ← 游戏状态
├─────────────────────────────────────┤
│ + PuzzleGame()                      │  ← 构造函数
│ + startNewGame(): void              │  ← 开始新游戏
│ + handleMove(row, col): void        │  ← 处理移动请求
│ + checkGameState(): void            │  ← 检查游戏状态
│ + resetGame(): void                 │  ← FR-012: 重置功能
└─────────────────────────────────────┘
```

### 2.3 PuzzleGameUI类 (对应NFR-002)
```
┌─────────────────────────────────────┐
│           PuzzleGameUI              │
├─────────────────────────────────────┤
│ - frame: JFrame                     │  ← Swing主窗口
│ - buttons: JButton[][]              │  ← FR-001: 4×4按钮网格
│ - statusLabel: JLabel               │  ← FR-014: 状态显示
│ - resetButton: JButton              │  ← FR-013: 重置按钮
│ - game: PuzzleGame                  │  ← Controller引用
├─────────────────────────────────────┤
│ + PuzzleGameUI(game: PuzzleGame)    │  ← 构造函数
│ + initializeUI(): void              │  ← 初始化界面
│ + updateDisplay(): void             │  ← 更新显示
│ + showWinMessage(): void            │  ← FR-011: 胜利提示
│ + start(): void                     │  ← 启动界面
└─────────────────────────────────────┘
```

### 2.4 Main类
```
┌─────────────────────────────────────┐
│              Main                   │
├─────────────────────────────────────┤
│ + main(args: String[]): void        │  ← 程序入口
└─────────────────────────────────────┘
```

## 3. 状态图设计 (对应FR-010)

```
    [开始]
       │
       ▼
┌─────────────┐
│  游戏初始化  │
└─────────────┘
       │
       ▼
┌─────────────┐
│   进行中     │◄──────┐
│ (PLAYING)   │       │
└─────────────┘       │
       │              │
       │ 移动方块       │ 重置游戏
       ▼              │
┌─────────────┐       │
│  检查胜利    │       │
└─────────────┘       │
       │              │
    ┌──▼──┐           │
    │胜利？│           │
    └─────┘           │
      │ │             │
   否 │ │ 是          │
      ▼ ▼             │
┌─────────┐  ┌─────────┐
│继续游戏 │  │  胜利   │
└─────────┘  │(WON)    │
      │      └─────────┘
      │            │
      └────────────┘
```

## 4. 时序图设计 (对应FR-005点击移动流程)

```
用户      PuzzleGameUI    PuzzleGame     GameBoard
 │            │              │             │
 │ 点击JButton │              │             │
 ├───────────►│              │             │
 │            │  handleMove  │             │
 │            ├─────────────►│             │
 │            │              │ isValidMove │
 │            │              ├────────────►│
 │            │              │   boolean   │
 │            │              │◄────────────┤
 │            │              │             │
 │            │   (如果有效)   │ moveNumber  │
 │            │              ├────────────►│
 │            │              │   boolean   │
 │            │              │◄────────────┤
 │            │              │ isWinning   │
 │            │              ├────────────►│
 │            │              │   boolean   │
 │            │              │◄────────────┤
 │            │ updateDisplay│             │
 │            │◄─────────────┤             │
 │            │              │             │
 │ Swing界面更新│              │             │
 │◄───────────┤              │             │
 │            │              │             │
```

## 5. 界面设计原型

### 5.1 主界面布局 (对应FR-001, FR-003)
```
┌─────────────────────────────────────┐
│          数字拼图游戏               │
├─────────────────────────────────────┤
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │  1  │ │  2  │ │  3  │ │  4  │   │  ← 4×4数字网格
│  └─────┘ └─────┘ └─────┘ └─────┘   │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │  5  │ │  6  │ │  7  │ │  8  │   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │  9  │ │ 10  │ │ 11  │ │ 12  │   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │ 13  │ │ 14  │ │ 15  │ │     │   │  ← 空白格(FR-004)
│  └─────┘ └─────┘ └─────┘ └─────┘   │
├─────────────────────────────────────┤
│  状态: 进行中           │ [重新开始] │  ← FR-014,FR-013
└─────────────────────────────────────┘
```

### 5.2 胜利状态界面 (对应FR-011)
```
┌─────────────────────────────────────┐
│          数字拼图游戏               │
├─────────────────────────────────────┤
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │  1  │ │  2  │ │  3  │ │  4  │   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │  5  │ │  6  │ │  7  │ │  8  │   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │  9  │ │ 10  │ │ 11  │ │ 12  │   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │ 13  │ │ 14  │ │ 15  │ │     │   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
├─────────────────────────────────────┤
│  🎉 恭喜您获得胜利! 🎉  │ [重新开始] │
└─────────────────────────────────────┘
```

## 6. 技术实现要点

### 6.1 核心算法设计
- **随机打乱算法**: 确保生成的初始状态有解
- **移动有效性判断**: 检查目标位置是否与空格相邻
- **胜利状态检测**: 验证数字1-15是否按序排列

### 6.2 性能优化 (对应NFR-005, NFR-006)
- 移动操作时间复杂度: O(1)
- 胜利判断时间复杂度: O(1) (使用计数器优化)
- 界面更新采用局部刷新策略

### 6.3 Swing界面技术要点 (对应NFR-002)
- 使用BorderLayout和GridLayout布局管理器
- JButton数组实现4×4网格
- ActionListener处理点击事件
- JDK 1.8内置Swing，无需外部依赖
- 使用JOptionPane显示胜利对话框

---

**设计文档版本**: v1.0  
**对应需求文档**: 数字拼图游戏需求文档 v1.0  
**设计日期**: 2025-08-11