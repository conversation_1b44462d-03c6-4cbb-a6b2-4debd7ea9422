import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * PuzzleGameUI类 - Swing用户界面
 * 对应设计文档中的View层，使用Swing框架实现用户界面
 * 
 * 对应需求：
 * - NFR-002: 使用Swing作为GUI框架（已从SWT改为Swing）
 * - FR-001,FR-003: 4×4按钮网格显示数字
 * - FR-004: 空白格视觉区分
 * - FR-013: 重置按钮
 * - FR-014: 状态显示
 * - FR-011: 胜利提示信息
 * - NFR-005,NFR-006: 响应时间要求
 */
public class PuzzleGameUI extends JFrame {
    private JButton[][] buttons;     // 4×4按钮网格 (对应FR-001)
    private JLabel statusLabel;      // 状态显示标签 (对应FR-014)
    private JButton resetButton;     // 重置按钮 (对应FR-013)
    private PuzzleGame game;         // Controller引用
    
    /**
     * 构造函数
     * @param game 游戏控制器对象
     */
    public PuzzleGameUI(PuzzleGame game) {
        this.game = game;
        game.setUI(this); // 设置UI引用到Controller
        initializeUI();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        setTitle("Puzzle Game");  // 使用英文避免乱码
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // 创建游戏网格
        createGameGrid();
        
        // 创建控制面板
        createControlPanel();
        
        // 初始化显示
        updateDisplay();
        
        // 设置窗口属性
        setResizable(false);
        pack();
        setLocationRelativeTo(null); // 居中显示
    }
    
    /**
     * 创建4×4游戏网格 (对应FR-001,FR-003)
     */
    private void createGameGrid() {
        JPanel gridPanel = new JPanel(new GridLayout(4, 4, 5, 5));
        gridPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));
        
        // 创建4×4按钮数组
        buttons = new JButton[4][4];
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                buttons[i][j] = new JButton();
                buttons[i][j].setPreferredSize(new Dimension(80, 80));
                buttons[i][j].setFont(new Font("Arial", Font.BOLD, 20));
                
                // 为每个按钮添加点击事件处理 (按照时序图流程)
                final int row = i;
                final int col = j;
                buttons[i][j].addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        // NFR-005: 确保响应时间不超过100毫秒
                        handleButtonClick(row, col);
                    }
                });
                
                gridPanel.add(buttons[i][j]);
            }
        }
        
        add(gridPanel, BorderLayout.CENTER);
    }
    
    /**
     * 创建控制面板 (对应FR-013,FR-014)
     */
    private void createControlPanel() {
        JPanel controlPanel = new JPanel(new BorderLayout());
        controlPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 20, 20));
        
        // 状态显示标签 (对应FR-014)
        statusLabel = new JLabel("Status: Playing", JLabel.CENTER);
        statusLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        
        // 重置按钮 (对应FR-013)
        resetButton = new JButton("Reset");
        resetButton.setFont(new Font("Arial", Font.PLAIN, 14));
        resetButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                game.resetGame();
            }
        });
        
        controlPanel.add(statusLabel, BorderLayout.CENTER);
        controlPanel.add(resetButton, BorderLayout.EAST);
        
        add(controlPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 处理按钮点击事件
     * @param row 被点击按钮的行坐标
     * @param col 被点击按钮的列坐标
     */
    private void handleButtonClick(int row, int col) {
        // 调用Controller处理移动请求
        game.handleMove(row, col);
    }
    
    /**
     * 更新界面显示
     */
    public void updateDisplay() {
        int[][] board = game.getCurrentBoard();
        
        // 更新按钮显示 (对应FR-003,FR-004)
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (board[i][j] == 0) {
                    // 空白格处理 (对应FR-004: 视觉区分)
                    buttons[i][j].setText("");
                    buttons[i][j].setBackground(Color.LIGHT_GRAY);
                    buttons[i][j].setEnabled(false);
                } else {
                    // 数字方块 (对应FR-003: 清晰显示数字)
                    buttons[i][j].setText(String.valueOf(board[i][j]));
                    buttons[i][j].setBackground(Color.WHITE);
                    buttons[i][j].setEnabled(true);
                    
                    // 可移动的方块用不同颜色提示
                    if (game.isValidMove(i, j)) {
                        buttons[i][j].setBackground(new Color(230, 255, 230)); // 浅绿色
                    }
                }
            }
        }
        
        // 更新状态显示 (对应FR-014)
        String status = game.getGameState() == PuzzleGame.GameState.WON ? "Won!" : "Playing";
        statusLabel.setText("Status: " + status);
        
        // 强制重绘
        repaint();
    }
    
    /**
     * 显示胜利消息 (对应FR-011: 胜利提示信息)
     */
    public void showWinMessage() {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                JOptionPane.showMessageDialog(
                    PuzzleGameUI.this,
                    "Congratulations! You Won!\n\nYou have successfully completed the puzzle!\nNumbers 1-15 are now in correct order.",
                    "Victory",
                    JOptionPane.INFORMATION_MESSAGE
                );
            }
        });
    }
    
    /**
     * 启动界面
     */
    public void start() {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                setVisible(true);
            }
        });
    }
}