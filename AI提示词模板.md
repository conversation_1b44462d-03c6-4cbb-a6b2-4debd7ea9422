# AI提示词模板

## 设计文档生成提示词

```
请根据"数字拼图游戏需求文档.md"生成完整的设计文档，要求：

1. 系统架构设计：
   - 按照需求NFR-004实现MVC模式
   - 明确各模块职责和接口

2. 详细类图设计：
   - 根据需求4.2节的核心类要求设计
   - GameBoard类：管理4×4网格数据（FR-001）
   - PuzzleGame类：游戏逻辑控制
   - PuzzleGameUI类：Swing界面实现（NFR-002）
   - Main类：程序入口

3. 状态图设计：
   - 体现FR-010胜利判断逻辑
   - 显示游戏状态转换

4. 时序图设计：
   - 详细展示FR-005点击移动的完整流程
   - 从用户点击到界面更新的全过程

5. 界面设计原型：
   - 4×4按钮网格布局（FR-001, FR-003）
   - 重置按钮位置（FR-013）

请确保设计的每个元素都能在需求文档中找到对应的需求编号。
```

## 代码生成提示词

### GameBoard类生成
```
请根据设计文档中的类图，生成GameBoard.java类，要求：
1. 严格按照类图中定义的属性和方法
2. 实现4×4数字网格的数据存储（对应FR-001）
3. 实现方块移动逻辑（对应FR-005, FR-006, FR-007）
4. 实现胜利状态判断（对应FR-010）
5. 实现随机打乱功能（对应FR-009）
请使用JDK 1.8兼容的语法。
```

### PuzzleGame类生成
```
请根据设计文档生成PuzzleGame.java类，要求：
1. 按照MVC模式作为Controller层（对应NFR-004）
2. 管理游戏状态转换（参考状态图设计）
3. 协调GameBoard和UI层的交互
4. 实现重置游戏功能（对应FR-012）
使用JDK 1.8语法。
```

### PuzzleGameUI类生成
```
请根据设计文档生成PuzzleGameUI.java类，要求：
1. 使用Swing框架（对应NFR-002）
2. 创建4×4按钮网格（对应FR-001, FR-003）
3. 实现点击事件处理（按照时序图流程）
4. 显示游戏状态和胜利提示（对应FR-011, FR-014）
5. 添加重置按钮（对应FR-013）
6. 确保响应时间符合性能要求（NFR-005, NFR-006）
使用JDK 1.8内置的Swing库。
```

### Main类生成
```
请根据设计文档生成Main.java类，要求：
1. 作为程序入口点
2. 初始化各个模块
3. 启动Swing界面
4. 确保程序能在JDK 1.8环境运行
```

## 编译运行命令

### 编译命令
```bash
javac *.java
```

### 运行命令
```bash
java Main
```

### 注意事项
- Swing是JDK内置库，无需额外classpath设置
- 支持JDK 1.8及以上版本
- 跨平台兼容（Windows/Linux/Mac）

## 故障排除

### 如果遇到编译错误
1. 检查JDK版本：`java -version`（需要1.8或更高）
2. 确认所有java文件在同一目录
3. 检查代码语法是否正确

### 如果遇到运行错误
1. 检查Main-Class是否正确
2. 确认有图形界面环境（非纯命令行）
3. 检查Swing相关代码是否正确

## 演示时间控制

- 设计文档生成：最多6分钟
- 代码生成：最多10分钟
- 如果AI响应过慢，可以说："为了时间控制，我们看关键部分"
- 如果生成内容过长，可以说："由于时间关系，我们重点看核心代码"