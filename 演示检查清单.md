# 演示前检查清单

## 演示前准备 ✓

### 环境检查
- [ ] 确认JDK 1.8已安装：`java -version`
- [ ] 确认有图形界面环境（非纯命令行）
- [ ] 确认演示电脑能够正常运行Java程序
- [ ] 确认AI工具可以正常访问和使用

### 文件准备
- [x] 需求文档已创建：`数字拼图游戏需求文档.md`
- [x] 演示脚本已准备：`演示操作脚本.md` 
- [x] AI提示词已准备：`AI提示词模板.md`
- [x] 检查清单已创建：`演示检查清单.md`

### 演示材料检查
- [ ] 打印版演示脚本（备用）
- [ ] 确认所有提示词可以快速复制粘贴
- [ ] 确认需求文档重点内容已标记

## 演示流程验证 

### 时间分配验证（总计20分钟）
- [ ] 第一阶段（需求展示）：2分钟 ✓
- [ ] 第二阶段（设计生成）：6分钟 ✓
- [ ] 第三阶段（代码生成）：10分钟 ✓
- [ ] 第四阶段（运行验证）：2分钟 ✓

### 关键演示点确认
- [ ] 需求文档中的FR-001, FR-005, FR-010, NFR-001, NFR-002(Swing)重点标记
- [ ] 设计文档生成时的需求对应关系说明准备
- [ ] 代码生成时的设计符合性验证说明准备
- [ ] 最终软件的需求符合性验证说明准备

## 技术环境检查

### Swing环境准备
- [ ] 确认JDK包含Swing库（JDK 1.8内置）
- [ ] 测试基础Swing程序能否运行
- [ ] 确认图形界面环境可用

### 编译环境
- [ ] 创建干净的工作目录
- [ ] 确认javac和java命令可用
- [ ] 测试编译和运行基础Swing程序

## 演示内容检查

### 需求文档质量
- [x] 包含明确的功能需求编号（FR-001到FR-015）
- [x] 包含明确的非功能需求编号（NFR-001到NFR-010）
- [x] 包含具体的技术约束（JDK 1.8, Swing）
- [x] 包含验收标准
- [x] 内容结构化，便于演示时快速定位

### AI提示词准备
- [x] 设计文档生成提示词包含需求引用
- [x] 代码生成提示词包含设计文档引用
- [x] 每个提示词都强调对应关系
- [x] 提示词长度适中，不会超时

### 演示脚本完整性
- [x] 每个步骤都有明确的操作指令
- [x] 每个步骤都有对应的讲解词
- [x] 重点强调需求→设计→代码的对应关系
- [x] 包含时间控制提醒

## 应急预案

### 如果AI响应过慢
- [ ] 准备简化版提示词
- [ ] 准备跳过某些详细步骤的说词
- [ ] 准备"为了时间控制，我们看关键部分"的过渡语

### 如果编译失败
- [ ] 检查JDK版本和环境变量
- [ ] 准备说明编译问题的说词
- [ ] 准备展示代码而不运行的备选方案

### 如果时间超时
- [ ] 确认每个阶段的最小必要演示内容
- [ ] 准备快速跳转到关键演示点的方法
- [ ] 准备简化版总结说词

## 演示效果检查

### 教学目标达成
- [ ] 清晰展示需求→设计的对应关系
- [ ] 清晰展示设计→代码的对应关系
- [ ] 清晰展示代码→软件的对应关系
- [ ] 突出AI理解和生成的准确性

### 观众体验
- [ ] 每个步骤都有明确的说明
- [ ] 重点内容有视觉高亮或标记
- [ ] 语速适中，便于跟随
- [ ] 有清晰的总结和回顾

## 最终确认

### 演示前30分钟
- [ ] 再次测试所有技术环境
- [ ] 确认所有文件都在正确位置
- [ ] 快速过一遍完整流程
- [ ] 确认时间控制在20分钟内

### 演示前5分钟
- [ ] 打开所有需要的文件和程序
- [ ] 准备好第一个AI提示词
- [ ] 调整屏幕显示和字体大小
- [ ] 深呼吸，准备开始

---

**重要提醒**：
1. 每个阶段结束后，一定要强调对应关系
2. 时间控制很重要，宁可省略细节也要完成整个流程  
3. 如果出现技术问题，保持冷静，用备用方案
4. 重点是展示AI的理解能力和生成质量，不是炫技