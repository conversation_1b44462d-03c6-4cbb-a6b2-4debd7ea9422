import java.util.Random;

/**
 * GameBoard类 - 数字拼图游戏的数据模型
 * 对应设计文档中的Model层，管理4×4数字网格数据
 * 
 * 对应需求：
 * - FR-001: 4×4数字网格数据存储
 * - FR-005,FR-006,FR-007: 方块移动逻辑
 * - FR-009: 随机打乱功能
 * - FR-010: 胜利状态判断
 * - FR-012: 重置游戏功能
 */
public class GameBoard {
    private int[][] board;      // 4×4数字网格 (对应FR-001)
    private int emptyRow;       // 空格行坐标
    private int emptyCol;       // 空格列坐标
    private Random random;
    
    public GameBoard() {
        board = new int[4][4];
        random = new Random();
        initializeBoard();
    }
    
    /**
     * 初始化游戏棋盘 (对应FR-009: 随机打乱)
     */
    public void initializeBoard() {
        // 先创建有序状态
        int num = 1;
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (i == 3 && j == 3) {
                    board[i][j] = 0; // 空格用0表示
                    emptyRow = i;
                    emptyCol = j;
                } else {
                    board[i][j] = num++;
                }
            }
        }
        
        // 随机打乱（确保有解）
        shuffleBoard();
    }
    
    /**
     * 随机打乱棋盘，确保生成的状态有解
     */
    private void shuffleBoard() {
        // 通过随机移动来打乱，确保有解
        for (int i = 0; i < 1000; i++) {
            // 随机选择一个可移动的方向
            int[] dx = {-1, 1, 0, 0};
            int[] dy = {0, 0, -1, 1};
            
            int direction = random.nextInt(4);
            int newRow = emptyRow + dx[direction];
            int newCol = emptyCol + dy[direction];
            
            if (isValidPosition(newRow, newCol)) {
                // 移动数字到空格位置
                board[emptyRow][emptyCol] = board[newRow][newCol];
                board[newRow][newCol] = 0;
                emptyRow = newRow;
                emptyCol = newCol;
            }
        }
    }
    
    /**
     * 检查坐标是否有效
     */
    private boolean isValidPosition(int row, int col) {
        return row >= 0 && row < 4 && col >= 0 && col < 4;
    }
    
    /**
     * 移动数字方块 (对应FR-005: 点击移动功能)
     * @param row 要移动的方块行坐标
     * @param col 要移动的方块列坐标
     * @return 是否移动成功
     */
    public boolean moveNumber(int row, int col) {
        if (!isValidMove(row, col)) {
            return false; // FR-008: 不可移动的方块无反应
        }
        
        // 交换数字和空格
        board[emptyRow][emptyCol] = board[row][col];
        board[row][col] = 0;
        emptyRow = row;
        emptyCol = col;
        
        return true;
    }
    
    /**
     * 判断移动是否有效 (对应FR-006,FR-007: 移动规则)
     * @param row 目标方块行坐标
     * @param col 目标方块列坐标
     * @return 是否可以移动
     */
    public boolean isValidMove(int row, int col) {
        if (!isValidPosition(row, col)) {
            return false;
        }
        
        // FR-006: 只有与空格水平或垂直相邻的方块才能移动
        // FR-007: 对角线相邻的方块不能移动
        int deltaRow = Math.abs(row - emptyRow);
        int deltaCol = Math.abs(col - emptyCol);
        
        return (deltaRow == 1 && deltaCol == 0) || (deltaRow == 0 && deltaCol == 1);
    }
    
    /**
     * 判断是否获胜 (对应FR-010: 胜利状态判断)
     * @return 是否达到胜利状态
     */
    public boolean isWinning() {
        // 检查数字1-15是否按顺序排列，空格在右下角
        int expected = 1;
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (i == 3 && j == 3) {
                    // 最后一个位置应该是空格(0)
                    if (board[i][j] != 0) {
                        return false;
                    }
                } else {
                    if (board[i][j] != expected) {
                        return false;
                    }
                    expected++;
                }
            }
        }
        return true;
    }
    
    /**
     * 获取当前棋盘状态
     * @return 棋盘的副本
     */
    public int[][] getBoard() {
        int[][] copy = new int[4][4];
        for (int i = 0; i < 4; i++) {
            System.arraycopy(board[i], 0, copy[i], 0, 4);
        }
        return copy;
    }
    
    /**
     * 重置游戏 (对应FR-012: 重新开始功能)
     */
    public void reset() {
        initializeBoard();
    }
    
    /**
     * 获取空格位置
     * @return 包含行和列的数组 [row, col]
     */
    public int[] getEmptyPosition() {
        return new int[]{emptyRow, emptyCol};
    }
}